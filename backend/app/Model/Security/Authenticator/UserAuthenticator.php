<?php declare(strict_types = 1);

namespace App\Model\Security\Authenticator;

use App\Domain\Player\PlayerQuery;
use App\Domain\User\User;
use App\Domain\User\UserQuery;
use App\Model\Database\EntityManager;
use App\Model\Database\QueryManager;
use App\Model\Exception\Runtime\AuthenticationException;
use App\Model\Security\Passwords;
use Nette\Security\Authenticator;
use Nette\Security\IIdentity;

final readonly class UserAuthenticator implements Authenticator {

	public function __construct(
		private QueryManager 	$qm,
		private EntityManager 	$em,
		private Passwords 		$passwords,
	) {}

	/**
	 * @throws AuthenticationException
	 */
	public function authenticate(string $user, string $password) : IIdentity {
		/** @var User|null $loggedUser */
		$loggedUser = $this->qm->findOne(UserQuery::ofEmail($user));

		if ($loggedUser === NULL) {
			throw new AuthenticationException('The nickname is incorrect.', self::IdentityNotFound);
		} elseif (!$loggedUser->isActivated()) {
			throw new AuthenticationException('The user is not active.', self::InvalidCredential);
		} elseif (!$this->passwords->verify($password, $loggedUser->getPasswordHash())) {
			throw new AuthenticationException('The password is incorrect.', self::InvalidCredential);
		}

		$loggedUser->changeLoggedAt();
		$this->em->flush();

		return $this->createIdentity($loggedUser);
	}

	protected function createIdentity(User $user) : IIdentity {
		/** @var Player|null $player */
		$player = $this->qm->findOne(PlayerQuery::ofUser($user));

		if ($player !== NULL) {
			return $player->toIdentity();
		}

		return $user->toIdentity();
	}

}
