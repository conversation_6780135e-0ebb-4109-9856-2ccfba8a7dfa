<?php declare(strict_types = 1);

namespace App\Domain\Building\Facade;

use App\Domain\Building\Building;
use App\Domain\Building\BuildingQuery;
use App\Domain\Building\BuildingType;
use App\Domain\Player\Player;
use App\Domain\Player\PlayerPositionProvider;
use App\Domain\Settings\Settings;
use App\Model\Database\EntityManager;
use App\Model\Database\QueryManager;
use App\Model\Exception\Logic\InvalidArgumentException;
use App\Model\Exception\Runtime\NotFoundException;

class BuildingUpgradeFacade {

	public function __construct(
		private readonly EntityManager 			$em,
		private readonly QueryManager 			$qm,
		private readonly PlayerPositionProvider $playerPositionProvider,
	) {}

	public function upgrade(Player $player, string $buildingId) : Building {
		/** @var Building|null $building */
		$building = $this->qm->findOne(BuildingQuery::ofPlayer($player)->withId($buildingId));

		if ($building === null) {
			throw new NotFoundException("Building not found");
		}

		if ($building->isBuilt() === FALSE) {
			throw new InvalidArgumentException('Upgrade is not possible');
		}

		if ($building->getLevel() >= BuildingType::getMaxLevel()) {
			throw new InvalidArgumentException('Building is already at max level');
		}

		// Check if player is in range of the building
		$this->playerPositionProvider->validateRange($player, $building->getPosition(), $player->getRange());

		$upgradeCost = $building->getType()->getBuildCost($building->getLevel() + 1);

		// Check if player has enough resources
		foreach ($upgradeCost as ['type' => $type, 'amount' => $amount]) {
			if ($player->hasResourceAmount($type, $amount) === FALSE) {
				throw new InvalidArgumentException('Not enough resources');
			}
		}

		// Decrease player resources
		foreach ($upgradeCost as ['type' => $type, 'amount' => $amount]) {
			$player->decreaseResource($type, $amount);
		}

		// Upgrade building
		$building->setUpgrading();

		$this->em->flush();

		return $building;
	}

}